{"name": "aizoku", "version": "0.1.0", "private": true, "license": "MIT", "description": "Offline AI, privacy friendly", "author": "Tokiniaina <<EMAIL>> (https://tokiniaina.vercel.app)", "main": "main.js", "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "vercel-build": "prisma generate && prisma migrate deploy && next build", "prisma:generate": "prisma generate", "postinstall": "prisma generate", "run:app": "node .next/standalone/server.js", "run:desktop": "electron . --no-sandbox"}, "build": {"appId": "com.aizoku.app", "productName": "AIZOKU", "files": ["dist/**/*", "main.js"], "directories": {"output": "release"}, "linux": {"target": ["AppImage", "deb"]}, "win": {"target": ["nsis"]}, "mac": {"target": ["dmg"]}}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.6.0", "@prisma/extension-accelerate": "^1.3.0", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "highlight.js": "^11.11.1", "jose": "^6.0.10", "katex": "^0.16.22", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "ollama": "^0.5.15", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.8", "react-spinners": "^0.17.0", "rehype-autolink-headings": "^7.1.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-parse": "^9.0.1", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "server-only": "^0.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "dotenv": "^16.5.0", "electron": "^35.2.1", "electron-builder": "^26.0.12", "eslint": "^9", "eslint-config-next": "15.3.1", "prisma": "^6.6.0", "tailwindcss": "^4", "tsx": "^4.19.3", "tw-animate-css": "^1.2.8", "typescript": "^5"}}