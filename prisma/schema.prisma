generator client {
  provider     = "prisma-client"
  output       = "../src/generated/prisma"
  moduleFormat = "esm"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id             String @id @default(cuid())
  username       String @unique
  password       String
  salt           String
  secretQuestion String
  secretAnswer   String
  chats          Chat[]

  @@map("users")
}

model Chat {
  id        String    @id @default(cuid())
  title     String
  userId    String
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  model     String
  messages  Message[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @default(now())
}

model Message {
  id        String   @id @default(cuid())
  chatId    String
  chat      Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  role      String
  content   String
  createdAt DateTime @default(now())
}
