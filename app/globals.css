@import "tailwindcss";
@import "tw-animate-css";

@plugin "@tailwindcss/typography";

@custom-variant dark (&:is(.dark *));

:root {
    --radius: 0.6rem;
    --background: oklch(1 0 89.876);
    --foreground: oklch(0.18 0.03 292);
    --muted: oklch(0.957 0.008 286.248);
    --muted-foreground: oklch(0.42 0.02 292);
    --popover: oklch(0.99 0.01 292);
    --popover-foreground: oklch(0.18 0.03 292);
    --card: oklch(0.98 0 0);
    --card-foreground: oklch(0.15 0.03 292);
    --border: oklch(0.901 0.029 313.343);
    --input: oklch(0.94 0.005 292);
    --primary: oklch(0.65 0.25 292);
    --primary-foreground: oklch(1 0 0);
    --secondary: oklch(0.88 0.07 292);
    --secondary-foreground: oklch(0.28 0.05 292);
    --accent: oklch(0.88 0.07 292);
    --accent-foreground: oklch(0.28 0.05 292);
    --destructive: oklch(0.45 0.29 25);
    --destructive-foreground: oklch(0.98 0.01 25);
    --ring: oklch(0.65 0.25 292);
    --chart-1: oklch(0.65 0.25 292);
    --chart-2: oklch(0.88 0.07 292);
    --chart-3: oklch(0.85 0.05 292);
    --chart-4: oklch(0.92 0.04 292);
    --chart-5: oklch(0.66 0.28 292);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.15 0.01 292);
    --sidebar-primary: oklch(0.58 0.22 292);
    --sidebar-primary-foreground: oklch(0.98 0.01 292);
    --sidebar-accent: oklch(0.95 0.01 292);
    --sidebar-accent-foreground: oklch(0.21 0.01 292);
    --sidebar-border: oklch(0.91 0.005 292);
    --sidebar-ring: oklch(0.58 0.22 292);
}

.dark {
    --background: oklch(0.15 0.02 292);
    --foreground: oklch(0.95 0.01 292);
    --muted: oklch(0.18 0.005 292);
    --muted-foreground: oklch(0.62 0.01 292);
    --popover: oklch(0.15 0.02 292);
    --popover-foreground: oklch(0.95 0.01 292);
    --card: oklch(0.12 0.01 292);
    --card-foreground: oklch(1 0 0);
    --border: oklch(0.256 0.01 268.292 / 0.93);
    --input: oklch(0.653 0 89.876 / 0.5);
    --primary: oklch(0.65 0.25 292);
    --primary-foreground: oklch(1 0 0);
    --secondary: oklch(0.2 0.01 292);
    --secondary-foreground: oklch(0.7 0.03 292);
    --accent: oklch(0.2 0.01 292);
    --accent-foreground: oklch(0.7 0.03 292);
    --destructive: oklch(0.42 0.27 25);
    --destructive-foreground: oklch(1 0 0);
    --ring: oklch(0.65 0.25 292);
    --chart-1: oklch(0.65 0.25 292);
    --chart-2: oklch(0.2 0.01 292);
    --chart-3: oklch(0.18 0.01 292);
    --chart-4: oklch(0.22 0.02 292);
    --chart-5: oklch(0.68 0.28 292);
    --sidebar: oklch(0.22 0.01 292);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.52 0.26 292);
    --sidebar-primary-foreground: oklch(0.98 0.01 292);
    --sidebar-accent: oklch(0.28 0.01 292);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.52 0.26 292);
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: system-ui, -apple-system, "Segoe UI", Roboto, sans-serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, monospace;
    --color-sidebar-ring: var(--sidebar-ring);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar: var(--sidebar);
    --color-chart-5: var(--chart-5);
    --color-chart-4: var(--chart-4);
    --color-chart-3: var(--chart-3);
    --color-chart-2: var(--chart-2);
    --color-chart-1: var(--chart-1);
    --color-ring: var(--ring);
    --color-input: var(--input);
    --color-border: var(--border);
    --color-destructive: var(--destructive);
    --color-accent-foreground: var(--accent-foreground);
    --color-accent: var(--accent);
    --color-muted-foreground: var(--muted-foreground);
    --color-muted: var(--muted);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-secondary: var(--secondary);
    --color-primary-foreground: var(--primary-foreground);
    --color-primary: var(--primary);
    --color-popover-foreground: var(--popover-foreground);
    --color-popover: var(--popover);
    --color-card-foreground: var(--card-foreground);
    --color-card: var(--card);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
}

body {
    background: var(--background);
    color: var(--foreground);
    font-family: var(--font-sans);
    font-size: 1rem;
    line-height: 1.6;
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground overflow-x-hidden;
    }
}
