# 📚 External Documentation Reference

This file gathers helpful external documentation links, tools, and guides used in the Aizoku project.

## 🧠 AI Models via Ollama

- Ollama: https://ollama.com
- Available models list: https://ollama.com/library

## 🗃️ Database & ORM

- Prisma ORM: https://www.prisma.io/docs
- SQLite: https://www.sqlite.org/docs.html

## ⚙️ Next.js & Server Actions

- Next.js Docs: https://nextjs.org/docs
- Server Actions: https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions

## 🧼 State Management

- Zustand: https://docs.pmnd.rs/zustand/getting-started/introduction

## 📝 Markdown Rendering

- React Markdown: https://github.com/remarkjs/react-markdown

## 💅 Styling

- Tailwind CSS: https://tailwindcss.com/docs
- ShadCn UI: https://ui.shadcn.com/docs

## ✅ Form Validation

- Zod: https://zod.dev/

Feel free to extend this list as the project grows!

